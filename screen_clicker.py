import pyautogui
import time
import cv2
import numpy as np
from PIL import Image
import os
import sys

class GreenCrosshairClicker:
    def __init__(self, confidence=0.85, scan_interval=0.05):
        """
        Initialize the green crosshair clicker

        Args:
            confidence: Confidence level for image matching (0.0 to 1.0)
            scan_interval: Time between scans in seconds
        """
        self.confidence = confidence
        self.scan_interval = scan_interval
        self.running = False

        # Enable PyAutoGUI failsafe (move mouse to corner to stop)
        pyautogui.FAILSAFE = True

        print(f"#00FF00 Green Crosshair Auto-Clicker initialized")
        print(f"Target color: #00FF00 (RGB: 0, 255, 0)")
        print(f"Scan interval: {scan_interval}s")
        print("Move mouse to top-left corner to stop the script")
        print("Will automatically click on #00FF00 green crosshairs!")
        print("Looking for exact #00FF00 color on screen...")

    def detect_green_crosshair(self, screenshot):
        """
        Detect #00FF00 green crosshair using HSV color detection.
        Returns the center coordinates if found, None otherwise.
        """
        # Convert PIL image to RGB numpy array
        img_array = np.array(screenshot.convert('RGB'))

        # Convert RGB to HSV
        hsv_image = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)

        # Define HSV range for neon green (#00FF00)
        lower_hsv = np.array([55, 200, 200])
        upper_hsv = np.array([75, 255, 255])

        # Mask pixels within HSV range
        mask = cv2.inRange(hsv_image, lower_hsv, upper_hsv)

        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        print(f"Found {len(contours)} green objects")

        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h

            print(f"Object {i+1}: area={area:.1f}, size={w}x{h}, aspect_ratio={aspect_ratio:.2f}, pos=({x},{y})")

            if 5 < area < 2000 and 0.5 < aspect_ratio < 2.0:
                center_x = x + w // 2
                center_y = y + h // 2
                print(f"*** CROSSHAIR DETECTED: center=({center_x},{center_y}) ***")
                return (center_x, center_y)

        return None

    
    def start_monitoring(self):
        """Start the continuous screen monitoring for green crosshairs"""
        self.running = True
        click_count = 0

        print("\nStarting green crosshair auto-clicking...")
        print("Press Ctrl+C to stop or move mouse to top-left corner")

        try:
            while self.running:
                try:
                    # Take a screenshot
                    screenshot = pyautogui.screenshot()

                    # Look for green crosshair
                    crosshair_pos = self.detect_green_crosshair(screenshot)

                    if crosshair_pos:
                        center_x, center_y = crosshair_pos

                        # Actually click on the green crosshair
                        pyautogui.click(center_x, center_y)
                        click_count += 1

                        print(f"CLICKED: #00FF00 crosshair #{click_count} at ({center_x}, {center_y})")

                        # Add a small delay after clicking to avoid rapid clicking
                        # time.sleep(0.3)
                    else:
                        # Only print this occasionally to avoid spam
                        if click_count % 50 == 0:  # Every 50 scans
                            print("Scanning for #00FF00 crosshairs...")

                except pyautogui.FailSafeException:
                    print("\nFailsafe triggered - mouse moved to corner. Stopping...")
                    break
                except Exception as e:
                    print(f"Error during detection: {e}")
                    time.sleep(0.1)

                # Wait before next scan
                # time.sleep(self.scan_interval)

        except KeyboardInterrupt:
            print("\nStopping green crosshair monitoring...")
        finally:
            self.running = False
            print(f"Total green crosshairs detected: {click_count}")

def main():
    print('starting in 3 seconds...')
    time.sleep(3)
    # Optional parameters from command line
    confidence = 0.85
    scan_interval = 0.05

    if len(sys.argv) > 1:
        try:
            confidence = float(sys.argv[1])
            print(f"Using custom confidence: {confidence}")
        except ValueError:
            print("Invalid confidence value, using default 0.85")

    if len(sys.argv) > 2:
        try:
            scan_interval = float(sys.argv[2])
            print(f"Using custom scan interval: {scan_interval}")
        except ValueError:
            print("Invalid scan interval, using default 0.05")

    print("#00FF00 Green Crosshair Auto-Clicker for War Thunder")
    print("Detects exact hex color #00FF00 (RGB: 0, 255, 0)")
    print("Usage: python screen_clicker.py [scan_interval]")
    print("Example: python screen_clicker.py 0.03")
    print()

    try:
        clicker = GreenCrosshairClicker(confidence, scan_interval)
        clicker.start_monitoring()
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
