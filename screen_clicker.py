import pyautogui
import time
import cv2
import numpy as np
from PIL import Image
import os
import sys
import threading
import queue
from collections import deque

class GreenCrosshairClicker:
    def __init__(self, confidence=0.85, scan_interval=0.05, max_threads=3):
        """
        Initialize the green crosshair clicker

        Args:
            confidence: Confidence level for image matching (0.0 to 1.0)
            scan_interval: Time between scans in seconds
            max_threads: Maximum number of worker threads for processing
        """
        self.confidence = confidence
        self.scan_interval = scan_interval
        self.max_threads = max_threads
        self.running = False

        # Threading components
        self.screenshot_queue = queue.Queue(maxsize=5)  # Limit queue size to prevent memory buildup
        self.click_queue = queue.Queue(maxsize=10)
        self.threads = []
        self.lock = threading.Lock()

        # Statistics
        self.stats = {
            'screenshots_taken': 0,
            'screenshots_processed': 0,
            'crosshairs_detected': 0,
            'clicks_performed': 0
        }

        # Recent click positions to avoid duplicate clicks
        self.recent_clicks = deque(maxlen=5)
        self.click_cooldown = 0.1  # Minimum time between clicks at same position

        # Enable PyAutoGUI failsafe (move mouse to corner to stop)
        pyautogui.FAILSAFE = True

        print(f"#00FF00 Green Crosshair Auto-Clicker initialized")
        print(f"Target color: #00FF00 (RGB: 0, 255, 0)")
        print(f"Scan interval: {scan_interval}s")
        print(f"Max threads: {max_threads}")
        print("Move mouse to top-left corner to stop the script")
        print("Will automatically click on #00FF00 green crosshairs!")
        print("Looking for exact #00FF00 color on screen...")

    def detect_green_crosshair(self, screenshot):
        """
        Detect #00FF00 green crosshair using HSV color detection.
        Returns the center coordinates if found, None otherwise.
        """
        # Convert PIL image to RGB numpy array
        img_array = np.array(screenshot.convert('RGB'))

        # Convert RGB to HSV
        hsv_image = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)

        # Define HSV range for neon green (#00FF00)
        lower_hsv = np.array([55, 200, 200])
        upper_hsv = np.array([75, 255, 255])

        # Mask pixels within HSV range
        mask = cv2.inRange(hsv_image, lower_hsv, upper_hsv)

        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        print(f"Found {len(contours)} green objects")

        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h

            print(f"Object {i+1}: area={area:.1f}, size={w}x{h}, aspect_ratio={aspect_ratio:.2f}, pos=({x},{y})")

            if 5 < area < 2000 and 0.5 < aspect_ratio < 2.0:
                center_x = x + w // 2
                center_y = y + h // 2
                print(f"*** CROSSHAIR DETECTED: center=({center_x},{center_y}) ***")
                return (center_x, center_y)

        return None

    def screenshot_worker(self):
        """Worker thread for taking screenshots"""
        while self.running:
            try:
                screenshot = pyautogui.screenshot()
                with self.lock:
                    self.stats['screenshots_taken'] += 1

                # Put screenshot in queue (non-blocking)
                try:
                    self.screenshot_queue.put(screenshot, timeout=0.001)
                except queue.Full:
                    # Skip this screenshot if queue is full
                    pass

                time.sleep(self.scan_interval)
            except Exception as e:
                print(f"Screenshot worker error: {e}")
                time.sleep(0.1)

    def detection_worker(self):
        """Worker thread for detecting crosshairs in screenshots"""
        while self.running:
            try:
                # Get screenshot from queue
                screenshot = self.screenshot_queue.get(timeout=0.1)

                with self.lock:
                    self.stats['screenshots_processed'] += 1

                # Detect crosshair
                crosshair_pos = self.detect_green_crosshair(screenshot)

                if crosshair_pos:
                    # Check if this position was recently clicked
                    current_time = time.time()
                    should_click = True

                    for click_time, (prev_x, prev_y) in self.recent_clicks:
                        if (current_time - click_time < self.click_cooldown and
                            abs(crosshair_pos[0] - prev_x) < 10 and
                            abs(crosshair_pos[1] - prev_y) < 10):
                            should_click = False
                            break

                    if should_click:
                        try:
                            self.click_queue.put(crosshair_pos, timeout=0.001)
                            with self.lock:
                                self.stats['crosshairs_detected'] += 1
                        except queue.Full:
                            pass  # Skip if click queue is full

                self.screenshot_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                print(f"Detection worker error: {e}")
                time.sleep(0.1)

    def click_worker(self):
        """Worker thread for performing clicks"""
        while self.running:
            try:
                # Get click position from queue
                click_pos = self.click_queue.get(timeout=0.1)
                center_x, center_y = click_pos

                # Perform the click
                pyautogui.click(center_x, center_y)

                # Record this click
                current_time = time.time()
                self.recent_clicks.append((current_time, click_pos))

                with self.lock:
                    self.stats['clicks_performed'] += 1
                    click_count = self.stats['clicks_performed']

                print(f"CLICKED: #00FF00 crosshair #{click_count} at ({center_x}, {center_y})")

                self.click_queue.task_done()

            except queue.Empty:
                continue
            except pyautogui.FailSafeException:
                print("\nFailsafe triggered - mouse moved to corner. Stopping...")
                self.running = False
                break
            except Exception as e:
                print(f"Click worker error: {e}")
                time.sleep(0.1)

    def print_stats(self):
        """Print current statistics"""
        with self.lock:
            stats = self.stats.copy()

        print(f"\n--- Statistics ---")
        print(f"Screenshots taken: {stats['screenshots_taken']}")
        print(f"Screenshots processed: {stats['screenshots_processed']}")
        print(f"Crosshairs detected: {stats['crosshairs_detected']}")
        print(f"Clicks performed: {stats['clicks_performed']}")
        print(f"Screenshot queue size: {self.screenshot_queue.qsize()}")
        print(f"Click queue size: {self.click_queue.qsize()}")

    def start_monitoring(self):
        """Start the threaded screen monitoring for green crosshairs"""
        self.running = True

        print("\nStarting threaded green crosshair auto-clicking...")
        print("Press Ctrl+C to stop or move mouse to top-left corner")
        print(f"Using {self.max_threads} worker threads for processing")

        try:
            # Start worker threads
            # Screenshot capture thread
            screenshot_thread = threading.Thread(target=self.screenshot_worker, daemon=True)
            screenshot_thread.start()
            self.threads.append(screenshot_thread)

            # Detection worker threads
            for i in range(self.max_threads - 1):  # Reserve one thread for clicking
                detection_thread = threading.Thread(target=self.detection_worker, daemon=True)
                detection_thread.start()
                self.threads.append(detection_thread)

            # Click worker thread
            click_thread = threading.Thread(target=self.click_worker, daemon=True)
            click_thread.start()
            self.threads.append(click_thread)

            print(f"Started {len(self.threads)} worker threads")

            # Main monitoring loop
            stats_interval = 5.0  # Print stats every 5 seconds
            last_stats_time = time.time()

            while self.running:
                try:
                    current_time = time.time()

                    # Print stats periodically
                    if current_time - last_stats_time >= stats_interval:
                        self.print_stats()
                        last_stats_time = current_time

                    time.sleep(0.1)  # Small sleep to prevent busy waiting

                except pyautogui.FailSafeException:
                    print("\nFailsafe triggered - mouse moved to corner. Stopping...")
                    break

        except KeyboardInterrupt:
            print("\nStopping green crosshair monitoring...")
        finally:
            self.running = False

            # Wait for threads to finish
            print("Waiting for worker threads to finish...")
            for thread in self.threads:
                thread.join(timeout=1.0)

            # Print final stats
            self.print_stats()
            print("All threads stopped.")

def main():
    print('starting in 3 seconds...')
    time.sleep(3)
    # Optional parameters from command line
    confidence = 0.85
    scan_interval = 0.05

    if len(sys.argv) > 1:
        try:
            confidence = float(sys.argv[1])
            print(f"Using custom confidence: {confidence}")
        except ValueError:
            print("Invalid confidence value, using default 0.85")

    if len(sys.argv) > 2:
        try:
            scan_interval = float(sys.argv[2])
            print(f"Using custom scan interval: {scan_interval}")
        except ValueError:
            print("Invalid scan interval, using default 0.05")

    print("#00FF00 Green Crosshair Auto-Clicker for War Thunder")
    print("Detects exact hex color #00FF00 (RGB: 0, 255, 0)")
    print("Usage: python screen_clicker.py [scan_interval]")
    print("Example: python screen_clicker.py 0.03")
    print()

    try:
        clicker = GreenCrosshairClicker(confidence, scan_interval)
        clicker.start_monitoring()
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
