# Yellow Crosshair Auto-Clicker

A Python script specifically designed to detect and automatically click on yellow crosshairs in War Thunder (or similar games). Uses advanced color detection to identify yellow crosshairs on your screen.

## Setup

1. **Install Python** (if not already installed)
   - Download from https://python.org
   - Make sure to check "Add Python to PATH" during installation

2. **Install required packages**
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. **Basic usage** (recommended settings)
   ```bash
   python screen_clicker.py
   ```

2. **Custom settings**
   ```bash
   python screen_clicker.py 0.9 0.03
   ```
   - First parameter: confidence level (0.0 to 1.0, default 0.85)
   - Second parameter: scan interval in seconds (default 0.05)

## How it works

The script uses computer vision to:
1. Take continuous screenshots of your screen
2. Detect yellow-colored objects (crosshairs) using HSV color space
3. Filter results by size and shape to identify crosshairs
4. Automatically click on detected yellow crosshairs

## Safety Features

- **Failsafe**: Move your mouse to the top-left corner of the screen to stop the script
- **Keyboard interrupt**: Press Ctrl+C to stop the script
- **Click counter**: Shows how many times it has clicked yellow crosshairs
- **Error handling**: Continues running even if temporary detection errors occur

## Optimization Tips

- **Higher confidence** (0.9-0.95): More precise detection, may miss faded crosshairs
- **Lower confidence** (0.7-0.85): More sensitive detection, may have false positives
- **Faster scanning** (0.03-0.05s): More responsive, higher CPU usage
- **Slower scanning** (0.1-0.2s): Less CPU usage, may miss quick crosshairs

## Perfect for War Thunder

This script is specifically optimized for War Thunder's yellow crosshairs:
- Detects various shades of yellow crosshairs
- Filters out non-crosshair yellow objects
- Fast response time for quick target acquisition
- Works in different lighting conditions

## Recommended Settings

For best performance in War Thunder:
```bash
python screen_clicker.py 0.85 0.05
```

## Troubleshooting

- **No clicks happening**: Yellow crosshairs might be too faded - try lowering confidence to 0.7
- **False clicks**: Increase confidence to 0.9 or check for other yellow UI elements
- **Missing quick targets**: Decrease scan interval to 0.03 seconds
- **High CPU usage**: Increase scan interval to 0.1 seconds
- **Script won't stop**: Move mouse to top-left corner or press Ctrl+C

## Color Detection Details

The script detects yellow colors in HSV range:
- Hue: 15-35 (covers yellow-orange to yellow-green)
- Saturation: 100-255 (bright colors only)
- Value: 100-255 (excludes dark yellows)
